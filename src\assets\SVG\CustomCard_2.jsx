/**
 * Componente SVG del ícono de flecha
 * Usado en botones y enlaces
 */
const CustomCard2 = ({width = 332, height = 347, className = ""}) => {

  return (
    <svg width={width} height={height} viewBox={`0 0 ${width} ${height}`} fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M0 293.176V42.985C0 32.5475 3.79773 22.4665 10.6845 14.6233C18.8458 5.32852 30.6157 0 42.985 0H204.732C213.665 0 222.32 3.10818 229.213 8.79152L231.459 10.6434C238.902 16.7809 243.214 25.9237 243.214 35.5712C243.214 45.3298 247.624 54.5656 255.214 60.6996L263.017 67.0054C269.844 72.5231 278.356 75.533 287.135 75.533C296.485 75.533 305.515 78.9478 312.525 85.1356L315.487 87.7501C325.986 97.0167 332 110.346 332 124.349V293.467C332 310.303 324.08 326.158 310.619 336.269C301.347 343.234 290.064 347 278.467 347H53.8244C40.4316 347 27.5198 342.007 17.6115 332.996C6.39451 322.796 0 308.337 0 293.176Z" fill="#B9F96F"/>
    </svg>
  );
};

export default CustomCard2;
