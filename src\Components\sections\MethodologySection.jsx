import { useState, useEffect } from 'react';
import ArrowIcon from '../../assets/SVG/ArrowIcon';
import './MethodologySection.css';
import CustomCard1 from '../../assets/SVG/CustomCard_1';
import CustomCard2 from '../../assets/SVG/CustomCard_2';
import CustomCard3 from '../../assets/SVG/CustomCard_3';
import CircleCustom1 from '../../assets/SVG/CircleCustom1';
import CircleCustom2 from '../../assets/SVG/CircleCustom2';
import CircleCustom3 from '../../assets/SVG/CircleCustom3';

/**
 * Sección de Metodología
 * Muestra los tres frameworks: IT Blackbook, Security Blackbook y Greta Playbook
 */
const MethodologySection = () => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => setIsVisible(true), 300);
    return () => clearTimeout(timer);
  }, []);

  const frameworks = [
    {
      title1: "IT",
      title2: "Blackbook",
      color: "dark",
      features: [
        "Proceso principal (venta completa)",
        "Mapeo de macro procesos",
        "Mapeo con sistemas",
        "Roadmap tecnológico + protección de datos",
        "Procesos de tecnología basado en APQC"
      ],
      customCard: CustomCard1,
      circle: CircleCustom1
    },
    {
      title1: "Security", 
      title2: "Blackbook", 
      color: "green",
      features: [
        "Security Vision",
        "Taller de identificación de activos",
        "Diseño se estrategia de mitigación",
        "Primera evaluación de seguridad",
        "Quick wins (1 mes max)",
        "Security Roadmap Security plan and check"
      ],
      customCard: CustomCard2,
      circle: CircleCustom2
    },
    {
      title1: "Greta",
      title2: "Playbook",
      color: "light",
      features: [
        "Metodologías (Scrum, JTBD, PVM)",
        "Gestión de proyectos",
        "Diseño de productos digitales",
        "Agilidad empresarial",
        "Transformación digital"
      ],
      customCard: CustomCard3,
      circle: CircleCustom3
    }
  ];

  return (
    <section className={`methodology-section ${isVisible ? 'visible' : ''}`}>
      <div className="methodology-container">
        {/* Header */}
        <div className="methodology-header flex gap-4 justify-between">
          <div className="flex flex-col gap-2">
            <h2 className="methodology-title">Nuestra Metodología</h2>
            <div className="title-underline"></div>
          </div>
          <div className="flex gap-2">
            <p className="methodology-description">
              Desarrollamos nuestros propios Frameworks en nuestro laboratorio para nuestros clientes 
              buscando la implementación de soluciones y servicios para la mejora de sus procesos.
            </p>
          </div>
        </div>

        {/* Frameworks Grid */}
        <div className="frameworks-grid">
          {frameworks.map((framework, index) => (
            <div 
              key={framework.title}
              // className={`framework-card ${framework.color}`}
              className="framework-card-ms justify-center items-center"
              style={{ '--delay': `${index * 0.3}s` }}
            >
              <div className="flex justify-center items-center">
                <framework.customCard  />
              </div>
              <div className={`framework-header ${framework.color}`}>
                <div className='flex flex-col'>
                  <h3 className="framework-title">{framework.title1}</h3>
                  <h3 className="framework-title">{framework.title2}</h3>
                </div>
              </div>
              <div className='absolute right-[3.3rem] top-[-0.1rem]'>
                <div className='flex justify-center items-center'>
                  <framework.circle />
                </div>
              </div>

              <div className={`framework-content ${framework.color}`}>
                <ul className="framework-features">
                  {framework.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="framework-feature">
                      • {feature}
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default MethodologySection;
